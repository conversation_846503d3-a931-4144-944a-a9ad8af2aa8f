多星座模式训练详细日志
================================================================================

实验时间: 2025-08-22 10:02:26
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练100000, 验证10000
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 11.494913
测试性能:
  平均收益率: 0.295441
  平均距离: 4.780425
  平均内存使用: 0.044016
  平均功耗: 0.145074
  综合性能评分: 0.469656
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_17_17_34\constellation_pnlstm_cooperative_transformer_L2H4_2025_08_21_17_17_34
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 11.362900
测试性能:
  平均收益率: 0.289889
  平均距离: 4.673947
  平均内存使用: 0.042135
  平均功耗: 0.142100
  综合性能评分: 0.469801
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_17_17_34\constellation_pnlstm_competitive_transformer_L2H4_2025_08_21_22_53_03
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
训练结果:
  最佳验证奖励: 11.438772
测试性能:
  平均收益率: 0.289350
  平均距离: 4.679989
  平均内存使用: 0.042941
  平均功耗: 0.141819
  综合性能评分: 0.461122
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_17_17_34\constellation_pnlstm_hybrid_transformer_L2H4_2025_08_22_04_26_55
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (11.4949)
最佳收益率模式: cooperative (0.2954)
最短距离模式: competitive (4.6739)
最低功耗模式: hybrid (0.1418)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
