多星座模式训练实验
================================================================================
实验时间: 2025_08_21_16_50_28
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-21 16:50:33
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/16, loss: 886.692, reward: 10.971, critic_reward: 22.972, revenue_rate: 0.2833, distance: 4.5613, memory: 0.0320, power: 0.1386, lr: 0.000100, took: 43.516s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 1, reward: 11.197, revenue_rate: 0.2891, distance: 4.6586, memory: 0.0568, power: 0.1420
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28 (验证集奖励: 11.1973)
[COOPERATIVE] 开始训练 Epoch 2/3
[COOPERATIVE] Epoch 2, Batch 10/16, loss: 39.637, reward: 11.451, critic_reward: 14.035, revenue_rate: 0.2946, distance: 4.6979, memory: 0.0251, power: 0.1416, lr: 0.000100, took: 43.404s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 2, reward: 10.703, revenue_rate: 0.2767, distance: 4.4795, memory: 0.0482, power: 0.1347
[COOPERATIVE] 开始训练 Epoch 3/3
[COOPERATIVE] Epoch 3, Batch 10/16, loss: 40.562, reward: 11.553, critic_reward: 10.008, revenue_rate: 0.2985, distance: 4.8454, memory: 0.0429, power: 0.1470, lr: 0.000100, took: 44.452s
[COOPERATIVE] 开始验证...
[COOPERATIVE] 验证完成 - Epoch 3, reward: 11.697, revenue_rate: 0.3023, distance: 4.8895, memory: 0.0420, power: 0.1475
[COOPERATIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28 (验证集奖励: 11.6965)
[COOPERATIVE] 训练完成
训练结束时间: 2025-08-21 16:54:37
训练总耗时: 0:04:04.478465
训练过程统计:
  最终训练奖励: 10.6561
  最佳验证奖励: 11.6965
  训练轮数完成: 48
  奖励提升: -0.3220
  平均每轮提升: -0.0067
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28\train_loss_reward.png
开始测试 cooperative 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-21 16:54:43
测试结束时间: 2025-08-21 16:54:53
测试耗时: 0:00:09.944777

COOPERATIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 11.6965
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28
测试结果:
  平均收益率: 0.2769
  平均距离: 4.3557
  平均内存使用: 0.0609
  平均功耗: 0.1352
模型信息:
  Actor参数: 3,862,536
  Critic参数: 494,285
  总参数: 4,356,821
综合性能评分: 0.4931
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/
==================================================

================================================================================
开始训练星座模式: COMPETITIVE
================================================================================
competitive 模式模型信息:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 competitive 模式...
训练开始时间: 2025-08-21 16:54:57
详细训练过程:
[COMPETITIVE] 开始训练 Epoch 1/3
[COMPETITIVE] Epoch 1, Batch 10/16, loss: 335.557, reward: 11.588, critic_reward: 10.764, revenue_rate: 0.2999, distance: 4.8645, memory: 0.0392, power: 0.1464, lr: 0.000100, took: 45.300s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 1, reward: 10.281, revenue_rate: 0.2657, distance: 4.3270, memory: 0.0513, power: 0.1288
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53 (验证集奖励: 10.2815)
[COMPETITIVE] 开始训练 Epoch 2/3
[COMPETITIVE] Epoch 2, Batch 10/16, loss: 38.604, reward: 11.440, critic_reward: 10.240, revenue_rate: 0.2959, distance: 4.8090, memory: 0.0350, power: 0.1447, lr: 0.000100, took: 44.641s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 2, reward: 11.004, revenue_rate: 0.2843, distance: 4.6085, memory: 0.0477, power: 0.1368
[COMPETITIVE] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53 (验证集奖励: 11.0035)
[COMPETITIVE] 开始训练 Epoch 3/3
[COMPETITIVE] Epoch 3, Batch 10/16, loss: 9.564, reward: 11.134, critic_reward: 11.388, revenue_rate: 0.2868, distance: 4.5961, memory: 0.0328, power: 0.1402, lr: 0.000100, took: 44.279s
[COMPETITIVE] 开始验证...
[COMPETITIVE] 验证完成 - Epoch 3, reward: 10.505, revenue_rate: 0.2725, distance: 4.4543, memory: 0.0709, power: 0.1353
[COMPETITIVE] 训练完成
训练结束时间: 2025-08-21 16:59:03
训练总耗时: 0:04:05.773531
训练过程统计:
  最终训练奖励: 9.9930
  最佳验证奖励: 11.0035
  训练轮数完成: 48
  奖励提升: -1.9566
  平均每轮提升: -0.0408
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53\train_loss_reward.png
开始测试 competitive 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-21 16:59:08
测试结束时间: 2025-08-21 16:59:20
测试耗时: 0:00:11.463800

COMPETITIVE 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 11.0035
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53
测试结果:
  平均收益率: 0.2791
  平均距离: 4.4710
  平均内存使用: 0.0525
  平均功耗: 0.1374
模型信息:
  Actor参数: 3,862,536
  Critic参数: 494,285
  总参数: 4,356,821
综合性能评分: 0.4605
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/
==================================================

================================================================================
开始训练星座模式: HYBRID
================================================================================
hybrid 模式模型信息:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
详细训练配置:
  数据集大小: 训练1000, 验证100
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 hybrid 模式...
训练开始时间: 2025-08-21 16:59:24
详细训练过程:
[HYBRID] 开始训练 Epoch 1/3
[HYBRID] Epoch 1, Batch 10/16, loss: 350.698, reward: 10.767, critic_reward: 23.311, revenue_rate: 0.2782, distance: 4.4952, memory: 0.0376, power: 0.1373, lr: 0.000100, took: 43.335s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 1, reward: 10.912, revenue_rate: 0.2811, distance: 4.5134, memory: 0.0388, power: 0.1362
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20 (验证集奖励: 10.9118)
[HYBRID] 开始训练 Epoch 2/3
[HYBRID] Epoch 2, Batch 10/16, loss: 5.453, reward: 11.023, critic_reward: 10.968, revenue_rate: 0.2831, distance: 4.5330, memory: 0.0321, power: 0.1378, lr: 0.000100, took: 45.407s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 2, reward: 11.295, revenue_rate: 0.2911, distance: 4.6656, memory: 0.0516, power: 0.1433
[HYBRID] 已保存新模型到 constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20 (验证集奖励: 11.2947)
[HYBRID] 开始训练 Epoch 3/3
[HYBRID] Epoch 3, Batch 10/16, loss: 10.222, reward: 11.099, critic_reward: 10.016, revenue_rate: 0.2860, distance: 4.6137, memory: 0.0338, power: 0.1407, lr: 0.000100, took: 43.441s
[HYBRID] 开始验证...
[HYBRID] 验证完成 - Epoch 3, reward: 10.755, revenue_rate: 0.2797, distance: 4.6515, memory: 0.0651, power: 0.1391
[HYBRID] 训练完成
训练结束时间: 2025-08-21 17:03:34
训练总耗时: 0:04:10.444287
训练过程统计:
  最终训练奖励: 9.8333
  最佳验证奖励: 11.2947
  训练轮数完成: 48
  奖励提升: -2.0361
  平均每轮提升: -0.0424
生成训练曲线图...
✓ 训练曲线图已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20\train_loss_reward.png
开始测试 hybrid 模式...
测试配置:
  测试数据大小: 100
  测试批次数: 2
  可视化样本数: 5
测试开始时间: 2025-08-21 17:03:39
测试结束时间: 2025-08-21 17:03:49
测试耗时: 0:00:09.879862

HYBRID 模式完整结果:
==================================================
训练结果:
  最佳验证奖励: 11.2947
  模型保存路径: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20
测试结果:
  平均收益率: 0.2793
  平均距离: 4.5103
  平均内存使用: 0.0356
  平均功耗: 0.1367
模型信息:
  Actor参数: 4,059,400
  Critic参数: 691,149
  总参数: 4,750,549
综合性能评分: 0.4518
文件输出:
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/
==================================================

================================================================================
生成对比分析
================================================================================
生成多模式训练曲线对比图...
✓ 多模式训练曲线图已保存

创建对比图表...
对比图表已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\comparison_results
对比结果已保存到:
  JSON文件: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\comparison_results\comparison_results.json
  文本报告: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\comparison_results\comparison_report.txt
详细训练日志已保存到: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\detailed_training_summary.txt

================================================================================
多星座模式训练实验总结
================================================================================
实验总耗时: 0:13:26.898703
成功训练模式数: 3/3

各模式详细对比:
模式           奖励       收益率      距离       内存       功耗       参数数       
----------------------------------------------------------------------
cooperative  11.6965  0.2769   4.3557   0.0609   0.1352   4,356,821 
competitive  11.0035  0.2791   4.4710   0.0525   0.1374   4,356,821 
hybrid       11.2947  0.2793   4.5103   0.0356   0.1367   4,750,549 

性能排名:
🏆 最高奖励: COOPERATIVE (11.6965)
💰 最高收益率: HYBRID (0.2793)
🚀 最短距离: COOPERATIVE (4.3557)
⚡ 最低功耗: COOPERATIVE (0.1352)

💡 推荐模式分析:
   如果追求最高奖励: COOPERATIVE
   如果追求最高收益率: HYBRID

📁 实验结果文件:
   主目录: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28
   对比分析: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\comparison_results
   全局日志: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\multi_mode_training_log.txt
   cooperative 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28
   competitive 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53
   hybrid 模式: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20
