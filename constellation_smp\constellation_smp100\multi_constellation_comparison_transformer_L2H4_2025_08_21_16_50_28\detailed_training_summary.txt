多星座模式训练详细日志
================================================================================

实验时间: 2025-08-21 17:03:54
实验配置:
  问题规模: 100节点, 3卫星
  训练参数: 3轮, 批次64, 学习率0.0001
  数据规模: 训练1000, 验证100
  使用Transformer: True
  Transformer配置: 2层, 4头

COOPERATIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 11.696512
测试性能:
  平均收益率: 0.276894
  平均距离: 4.355665
  平均内存使用: 0.060909
  平均功耗: 0.135181
  综合性能评分: 0.493061
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_cooperative_transformer_L2H4_2025_08_21_16_50_28
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_cooperative/

COMPETITIVE 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 3,862,536
  Critic参数数量: 494,285
  总参数数量: 4,356,821
训练结果:
  最佳验证奖励: 11.003510
测试性能:
  平均收益率: 0.279093
  平均距离: 4.470956
  平均内存使用: 0.052507
  平均功耗: 0.137435
  综合性能评分: 0.460484
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_competitive_transformer_L2H4_2025_08_21_16_54_53
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_competitive/

HYBRID 模式详细结果:
--------------------------------------------------
模型架构:
  Actor参数数量: 4,059,400
  Critic参数数量: 691,149
  总参数数量: 4,750,549
训练结果:
  最佳验证奖励: 11.294736
测试性能:
  平均收益率: 0.279303
  平均距离: 4.510262
  平均内存使用: 0.035590
  平均功耗: 0.136666
  综合性能评分: 0.451774
文件路径:
  模型保存: constellation_smp\constellation_smp100\multi_constellation_comparison_transformer_L2H4_2025_08_21_16_50_28\constellation_gpnlstm_hybrid_transformer_L2H4_2025_08_21_16_59_20
  权重文件: actor.pt, critic.pt
  训练日志: log.txt
  测试结果: test_hybrid/

对比分析:
==================================================
最佳奖励模式: cooperative (11.6965)
最佳收益率模式: hybrid (0.2793)
最短距离模式: cooperative (4.3557)
最低功耗模式: cooperative (0.1352)

推荐使用: cooperative 模式
推荐理由: 在关键性能指标上表现最佳
