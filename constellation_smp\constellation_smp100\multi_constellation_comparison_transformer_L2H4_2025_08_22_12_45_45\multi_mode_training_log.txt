多星座模式训练实验
================================================================================
实验时间: 2025_08_22_12_45_45
设备: cuda
问题规模: 100节点, 3卫星
训练配置: 3轮, 批次大小64
使用Transformer: True
Transformer配置: 2层, 4头

================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 3,730,953
  Critic参数数量: 494,285
  总参数数量: 4,225,238
详细训练配置:
  数据集大小: 训练100000, 验证10000
  学习率: Actor=0.0001, Critic=0.0002
  批次大小: 64
  训练轮数: 3
  梯度裁剪: 1
  权重衰减: 0.0001
  随机种子: 12346
  内存总量: 0.3
  功率总量: 5
开始训练 cooperative 模式...
训练开始时间: 2025-08-22 12:52:17
详细训练过程:
[COOPERATIVE] 开始训练 Epoch 1/3
[COOPERATIVE] Epoch 1, Batch 10/1563, loss: 558.833, reward: 11.218, critic_reward: 6.253, revenue_rate: 0.2902, distance: 4.7262, memory: 0.0400, power: 0.1421, lr: 0.000100, took: 43.156s
[COOPERATIVE] Epoch 1, Batch 20/1563, loss: 62.126, reward: 11.074, critic_reward: 12.294, revenue_rate: 0.2862, distance: 4.6437, memory: 0.0341, power: 0.1414, lr: 0.000100, took: 36.794s
[COOPERATIVE] Epoch 1, Batch 30/1563, loss: 15.601, reward: 11.422, critic_reward: 11.883, revenue_rate: 0.2943, distance: 4.7202, memory: 0.0350, power: 0.1426, lr: 0.000100, took: 37.354s
[COOPERATIVE] Epoch 1, Batch 40/1563, loss: 7.491, reward: 11.064, critic_reward: 10.985, revenue_rate: 0.2872, distance: 4.6234, memory: 0.0492, power: 0.1404, lr: 0.000100, took: 36.872s
